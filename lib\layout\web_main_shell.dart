import 'package:flutter/material.dart';
import 'package:octasync_client/imports.dart';
import 'package:octasync_client/providers/router/base_router_provider.dart';
import 'package:provider/provider.dart';
import 'package:octasync_client/models/route_item.dart';
import 'package:jyt_components_package/jyt_components_package.dart';

/// 路由工具类
///
/// 提供路由路径拼接和路由匹配等通用功能
class RouteUtils {
  /// 拼接父路径和子路径
  ///
  /// 如果父路径以'/'结尾，则直接拼接；否则在中间添加'/'
  static String joinPaths(String parentPath, String childPath) {
    return parentPath.endsWith('/') ? '$parentPath$childPath' : '$parentPath/$childPath';
  }

  /// 检查当前路径是否匹配目标路径
  ///
  /// 如果完全匹配或者是目标路径的子路径，则返回true
  static bool isPathActive(String targetPath, String currentPath) {
    // 精确匹配路径
    if (currentPath == targetPath) return true;

    // 检查是否为子路径
    if (currentPath.startsWith('$targetPath/')) return true;

    return false;
  }
}

/// 菜单样式帮助类
///
/// 用于统一管理菜单项的样式
class MenuStyleHelper {
  /// 获取菜单项文本样式
  static TextStyle getMenuItemTextStyle({
    required BuildContext context,
    required bool isActive,
    required bool isSubMenu,
  }) {
    return TextStyle(
      fontSize: isSubMenu ? 13 : 14,
      color: isActive ? AppColors.primary : context.textPrimary,
      fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
    );
  }

  /// 获取菜单项图标颜色过滤器
  static ColorFilter getMenuItemIconColorFilter({
    required BuildContext context,
    required bool isActive,
  }) {
    return ColorFilter.mode(isActive ? AppColors.primary : context.icon300, BlendMode.srcIn);
  }

  /// 获取菜单项图标颜色
  static Color getMenuItemIconColor({required BuildContext context, required bool isActive}) {
    return isActive ? AppColors.primary : context.icon300;
  }

  /// 获取菜单项容器装饰
  static BoxDecoration getBorderDecoration(BuildContext context) {
    return BoxDecoration(border: Border(bottom: BorderSide(color: context.border200)));
  }
}

/// Web端主应用外壳组件
///
/// 用于显示Web应用程序的导航菜单和页面内容。
/// 作为ShellRoute的包装器，它包含:
/// 1. 左侧的垂直菜单列表
/// 2. 主内容区域
///
/// 此组件仅用于显示菜单的路由，不显示菜单的路由使用其他包装器。
class WebMainShell extends StatefulWidget {
  const WebMainShell({required this.child, super.key});

  final Widget child;

  @override
  State<WebMainShell> createState() => _WebMainShellState();
}

class _WebMainShellState extends State<WebMainShell> {
  /// 菜单是否处于折叠状态
  bool isCollapsed = false;

  /// 标记是否已经根据屏幕尺寸自动设置过折叠状态
  bool _hasAutoCollapsed = false;

  /// 切换菜单折叠状态
  void _toggleMenuCollapsed() {
    setState(() {
      isCollapsed = !isCollapsed;
    });
  }

  @override
  Widget build(BuildContext context) {
    // 判断屏幕宽度，设置响应式布局
    final screenWidth = MediaQuery.of(context).size.width;
    final bool isSmallScreen = screenWidth < 768;

    // 在小屏幕下首次加载时默认折叠菜单，但仅执行一次
    if (isSmallScreen && !isCollapsed && !_hasAutoCollapsed) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        setState(() {
          isCollapsed = true;
          _hasAutoCollapsed = true; // 标记已执行过自动折叠
        });
      });
    }

    return Scaffold(
      body: Row(
        children: [
          // 左侧菜单
          _buildSideMenu(context),

          // 右侧区域
          Expanded(
            child: Column(
              children: [
                // 顶部导航栏
                _buildAppBar(context),
                // 内容区域(不能在这个位置加边距否则Loading遮罩有间隙)
                Expanded(child: Container(color: context.backgroundWhite, child: widget.child)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建左侧固定菜单
  Widget _buildSideMenu(BuildContext context) {
    final double menuWidth = isCollapsed ? 64 : 220;

    /// 折叠菜单按钮
    final Widget collapseButton = ClipRRect(
      borderRadius: BorderRadius.circular(AppRadiusSize.radius4),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _toggleMenuCollapsed,
          child: Padding(
            padding: EdgeInsets.all(4),
            child: Icon(isCollapsed ? Icons.menu : Icons.menu_open, size: AppIconSize.medium),
          ),
        ),
      ),
    );

    return Container(
      width: menuWidth,
      height: double.infinity,
      decoration: BoxDecoration(
        color: context.backgroundWhite,
        border: Border(right: BorderSide(color: context.border200)),
      ),
      child: Column(
        children: [
          // 顶部标题区域
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            height: 48,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // 折叠状态下只显示折叠图标按钮，展开状态下显示标题
                if (isCollapsed)
                  collapseButton
                else
                  Expanded(
                    child: Text(
                      '企业管理后台',
                      style: Theme.of(context).textTheme.headlineMedium,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),

                // 展开状态下在右侧显示折叠按钮
                if (!isCollapsed) collapseButton,
              ],
            ),
          ),

          // 分隔线
          Divider(height: 1),

          // 菜单列表
          Expanded(child: WebSideMenu(isCollapsed: isCollapsed)),
        ],
      ),
    );
  }

  /// AppBar
  Widget _buildAppBar(BuildContext context) {
    final routerProvider = context.watch<BaseRouterProvider>();
    final String location = GoRouterState.of(context).uri.path;

    // 查找当前路由的标题
    String currentLabel = _findRouteTitle(routerProvider.getMenuRoutes, location);

    return Container(
      height: 49,
      decoration: BoxDecoration(
        color: context.backgroundWhite,
        border: Border(bottom: BorderSide(color: context.border200)),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16),
      alignment: Alignment.centerLeft,
      child: Text(currentLabel, style: Theme.of(context).textTheme.headlineMedium),
    );
  }

  /// 根据路径查找路由标题
  String _findRouteTitle(List<RouteItem> routes, String location) {
    // 首先检查精确匹配
    for (var route in routes) {
      if (location == route.path) {
        return route.title ?? '';
      }
    }

    // 然后检查子路由
    for (var route in routes) {
      if (route.routes.isNotEmpty) {
        for (var subRoute in route.routes) {
          if (subRoute is RouteItem) {
            final subPath = RouteUtils.joinPaths(route.path, subRoute.path);

            if (location == subPath || location.startsWith('$subPath/')) {
              return subRoute.title ?? '';
            }
          }
        }
      }
    }

    return '';
  }
}

/// Web端侧边菜单组件
class WebSideMenu extends StatefulWidget {
  const WebSideMenu({required this.isCollapsed, super.key});

  final bool isCollapsed;

  @override
  State<WebSideMenu> createState() => _WebSideMenuState();
}

class _WebSideMenuState extends State<WebSideMenu> {
  /// 保存展开状态的Set
  final Set<String> _expandedMenus = {};

  /// 切换菜单展开状态
  void _toggleMenuExpanded(String path) {
    setState(() {
      if (_expandedMenus.contains(path)) {
        _expandedMenus.remove(path);
      } else {
        _expandedMenus.add(path);
      }
    });
  }

  /// 自动展开当前激活路径的父菜单
  void _expandActiveParentMenus(List<RouteItem> routes) {
    final String location = GoRouterState.of(context).uri.path;

    // 找出所有需要展开的父级菜单
    for (var route in routes) {
      if (route.routes.isNotEmpty) {
        // 检查子路由是否包含当前位置
        bool hasActiveChild = route.routes.any((subRoute) {
          if (subRoute is RouteItem) {
            final subPath = RouteUtils.joinPaths(route.path, subRoute.path);
            return location.startsWith(subPath);
          }
          return false;
        });

        if (hasActiveChild) {
          _expandedMenus.add(route.path);
        }
      }
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final routes = context.read<BaseRouterProvider>().getMenuRoutes;
    _expandActiveParentMenus(routes);
  }

  @override
  Widget build(BuildContext context) {
    final routerProvider = context.watch<BaseRouterProvider>();
    final routes = _getSortedRoutes(routerProvider.getMenuRoutes);

    return ListView(
      padding: const EdgeInsets.only(bottom: 5, right: 5, left: 5),
      children: _buildMenuItems(routes),
    );
  }

  /// 获取排序后的路由列表
  List<RouteItem> _getSortedRoutes(List<RouteItem> routes) {
    return routes.toList()..sort((a, b) => (a.order ?? 99).compareTo(b.order ?? 99));
  }

  /// 构建菜单项列表
  List<Widget> _buildMenuItems(List<RouteItem> routes) {
    return routes
        .map(
          (route) => WebMenuItem(
            route: route,
            isExpanded: _expandedMenus.contains(route.path),
            onToggleExpanded: () => _toggleMenuExpanded(route.path),
            isCollapsed: widget.isCollapsed,
            isSubMenu: false,
          ),
        )
        .toList();
  }
}

/// Web菜单项组件
class WebMenuItem extends StatelessWidget {
  const WebMenuItem({
    required this.route,
    required this.isCollapsed,
    this.isExpanded = false,
    this.onToggleExpanded,
    this.isSubMenu = false,
    this.parentPath,
    super.key,
  });

  final RouteItem route;
  final bool isExpanded;
  final VoidCallback? onToggleExpanded;
  final bool isSubMenu;
  final String? parentPath;
  final bool isCollapsed;

  @override
  Widget build(BuildContext context) {
    /// 是否有子菜单
    final hasChildren = route.routes.isNotEmpty;

    /// 是否处于激活状态
    final isActive = _isRouteActive(context);

    return Padding(
      padding: const EdgeInsets.only(top: 4),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 菜单项
          _buildMenuItem(context, isActive, hasChildren),

          // 子菜单 - 使用动画展开/折叠，在折叠状态下不显示子菜单
          if (!isCollapsed && hasChildren)
            AnimatedCrossFade(
              duration: const Duration(milliseconds: 300),
              crossFadeState: isExpanded ? CrossFadeState.showSecond : CrossFadeState.showFirst,
              firstChild: Container(), // 折叠状态
              secondChild: _buildSubMenus(context), // 展开状态
              firstCurve: Curves.easeOutQuad,
              secondCurve: Curves.easeInQuad,
              sizeCurve: Curves.easeInOutQuad,
            ),
        ],
      ),
    );
  }

  /// 处理折叠状态下菜单项点击
  void _handleCollapsedMenuClick(BuildContext context, bool hasChildren) {
    if (!hasChildren) {
      // 没有子菜单，直接导航
      context.go(_getFullPath());
    } else {
      // 有子菜单，显示弹出窗口
      final RenderBox renderBox = context.findRenderObject() as RenderBox;
      final Offset position = renderBox.localToGlobal(Offset.zero);
      final double menuItemWidth = renderBox.size.width;

      // 在显示弹出窗口前获取当前路径
      final String currentLocation = GoRouterState.of(context).uri.path;

      // 使用PopupOverlay.showAtPosition在菜单项右侧显示弹出窗口
      PopupOverlay.showAtPosition(
        context: context,
        position: Offset(position.dx + menuItemWidth, position.dy),
        constraints: const BoxConstraints(minWidth: 180, maxWidth: 250, maxHeight: 400),
        elevation: 4,
        backgroundColor: context.backgroundWhite,
        borderColor: context.border200,
        builder: (dialogContext, close) {
          // 在弹出菜单内创建带标题的子菜单列表，并传递当前路径
          return _buildPopupMenuContent(dialogContext, close, currentLocation);
        },
      );
    }
  }

  /// 构建弹出菜单内容
  Widget _buildPopupMenuContent(BuildContext context, VoidCallback close, String currentLocation) {
    // 避免直接使用GoRouterState.of(context)
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 主菜单标题
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
          decoration: BoxDecoration(
            color: context.background100,
            border: Border(bottom: BorderSide(color: context.border200)),
          ),
          child: Text(
            route.title ?? '',
            style: TextStyle(fontWeight: FontWeight.bold, color: context.textPrimary),
          ),
        ),

        // 子菜单项
        ...route.routes.map((subRoute) {
          if (subRoute is RouteItem) {
            return _buildPopupMenuItem(context, subRoute, close, currentLocation);
          }
          return Container();
        }).toList(),
      ],
    );
  }

  /// 构建共享菜单项行
  Widget _buildMenuItemRow({
    required BuildContext context,
    required RouteItem itemRoute,
    required bool isActive,
    required bool isItemSubMenu,
    bool showIconOnly = false,
  }) {
    return Row(
      mainAxisAlignment: showIconOnly ? MainAxisAlignment.center : MainAxisAlignment.start,
      children: [
        // 图标
        if (itemRoute.icon != null)
          Padding(
            padding: EdgeInsets.only(right: showIconOnly ? 0 : 12),
            child: Icon(
              itemRoute.icon!,
              size: isItemSubMenu ? AppIconSize.small : AppIconSize.medium,
              color: MenuStyleHelper.getMenuItemIconColor(context: context, isActive: isActive),
            ),
          ),

        // 标题 - 非仅图标模式下显示
        if (!showIconOnly)
          Expanded(
            child: Text(
              itemRoute.title ?? '',
              style: MenuStyleHelper.getMenuItemTextStyle(
                context: context,
                isActive: isActive,
                isSubMenu: isItemSubMenu,
              ),
            ),
          ),
      ],
    );
  }

  /// 构建弹出菜单中的子菜单项
  Widget _buildPopupMenuItem(
    BuildContext context,
    RouteItem subRoute,
    VoidCallback close,
    String currentLocation,
  ) {
    // 计算完整路径
    final String fullPath = _getSubRoutePath(subRoute);

    // 使用传入的currentLocation判断是否激活，而非通过context获取
    final bool isActive = RouteUtils.isPathActive(fullPath, currentLocation);

    return InkWell(
      onTap: () {
        // 关闭弹窗并导航
        close();
        context.go(fullPath);
      },
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: _buildMenuItemRow(
          context: context,
          itemRoute: subRoute,
          isActive: isActive,
          isItemSubMenu: true,
          showIconOnly: false,
        ),
      ),
    );
  }

  /// 获取子路由完整路径
  String _getSubRoutePath(RouteItem subRoute) {
    final parentFullPath = _getFullPath();
    return RouteUtils.joinPaths(parentFullPath, subRoute.path);
  }

  /// 构建菜单项
  Widget _buildMenuItem(BuildContext context, bool isActive, bool hasChildren) {
    final activeColor = hasChildren || !isActive ? Colors.transparent : context.activeGrayColor;

    // 菜单项内容
    Widget menuContent = Container(
      padding: EdgeInsets.symmetric(horizontal: isCollapsed ? 8 : 16, vertical: 12),
      child: Row(
        mainAxisAlignment: isCollapsed ? MainAxisAlignment.center : MainAxisAlignment.start,
        children: [
          // 使用共享行组件
          if (isCollapsed)
            // 折叠状态只显示图标
            if (route.icon != null)
              Icon(
                route.icon!,
                size: isSubMenu ? AppIconSize.small : AppIconSize.medium,
                color: MenuStyleHelper.getMenuItemIconColor(context: context, isActive: isActive),
              )
            else
              Container()
          else
            Expanded(
              child: _buildMenuItemRow(
                context: context,
                itemRoute: route,
                isActive: isActive,
                isItemSubMenu: isSubMenu,
                showIconOnly: false,
              ),
            ),

          // 展开/折叠箭头 - 非折叠状态且有子菜单时显示
          if (!isCollapsed && hasChildren)
            AnimatedRotation(
              turns: isExpanded ? 0.5 : 0,
              duration: const Duration(milliseconds: 300),
              child: Icon(Icons.keyboard_arrow_down, size: 16, color: context.icon100),
            ),
        ],
      ),
    );

    // 将菜单内容包装在InkWell中以处理点击事件
    Widget clickableMenu = ClipRRect(
      borderRadius: BorderRadius.circular(AppRadiusSize.radius4),
      child: Material(
        color: activeColor,
        child: InkWell(
          onTap: () {
            // 根据折叠状态处理点击事件
            if (isCollapsed) {
              _handleCollapsedMenuClick(context, hasChildren);
            } else {
              if (hasChildren) {
                onToggleExpanded?.call();
              } else {
                context.go(_getFullPath());
              }
            }
          },
          hoverColor: context.activeGrayColor.withOpacity(0.5),
          splashColor: context.activeGrayColor,
          child: menuContent,
        ),
      ),
    );

    // 在折叠状态下添加提示
    if (isCollapsed) {
      return Tooltip(
        message: route.title ?? '',
        preferBelow: false,
        verticalOffset: 15,
        margin: const EdgeInsets.only(left: 15),
        child: clickableMenu,
      );
    }

    return clickableMenu;
  }

  /// 构建子菜单
  Widget _buildSubMenus(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 8),
      child: Column(
        children: [
          for (var subRoute in route.routes)
            if (subRoute is RouteItem)
              WebMenuItem(
                route: subRoute,
                isSubMenu: true,
                isExpanded: false,
                parentPath: route.path, // 传递当前路由的路径作为子路由的父路径
                isCollapsed: isCollapsed,
              ),
        ],
      ),
    );
  }

  /// 获取完整路径，根据是否为子菜单确定路径
  String _getFullPath() {
    // 如果是子菜单并且有父路径
    if (isSubMenu && parentPath != null) {
      return RouteUtils.joinPaths(parentPath!, route.path);
    }

    // 顶级菜单直接返回路径
    return route.path;
  }

  /// 判断路由是否处于活动状态
  bool _isRouteActive(BuildContext context) {
    try {
      final String location = GoRouterState.of(context).uri.path;
      final fullPath = _getFullPath();

      return RouteUtils.isPathActive(fullPath, location);
    } catch (e) {
      // GoRouterState.of抛出异常时返回false
      return false;
    }
  }
}
