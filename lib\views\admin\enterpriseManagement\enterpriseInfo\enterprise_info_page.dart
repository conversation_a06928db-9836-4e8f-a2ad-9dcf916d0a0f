import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:octasync_client/api/enterprise_config.dart';
import 'package:octasync_client/commons/image_crop_dialog.dart';
import 'package:octasync_client/models/upload/upload_response.dart';
import 'package:octasync_client/views/login/login_page.dart';
import 'package:jyt_components_package/jyt_components_package.dart';

class EnterpriseInfoPage extends StatefulWidget {
  const EnterpriseInfoPage({super.key});

  @override
  State<EnterpriseInfoPage> createState() => _EnterpriseInfoPageState();
}

class _EnterpriseInfoPageState extends State<EnterpriseInfoPage> {
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _describeController = TextEditingController();
  bool _isLoading = false;
  EnterpriseDetail _enterpriseDetail = EnterpriseDetail();
  EnterpriseDetail _oldEnterpriseDetail = EnterpriseDetail();

  /// label宽度
  final double labelWidth = 90;

  /// 头像悬停状态变量
  bool _isAvatarHovering = false;

  /// 标识悬停状态变量
  bool _isSignHovering = false;

  @override
  void initState() {
    super.initState();
    _getDetail();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _describeController.dispose();
    super.dispose();
  }

  /// 获取企业详情
  _getDetail() async {
    try {
      setState(() => _isLoading = true);
      final res = await EnterpriseConfigApi.getDetail({});
      _enterpriseDetail = EnterpriseDetail.fromJson(res);
      _oldEnterpriseDetail = EnterpriseDetail.fromJson(res);

      _nameController.text = _enterpriseDetail.enterpriseName;
      _describeController.text = _enterpriseDetail.enterpriseDescribe;
      setState(() => _isLoading = false);
    } catch (e) {
      setState(() => _isLoading = false);
    }
  }

  /// 编辑企业信息
  _editEnterpriseInfo() async {
    if (_oldEnterpriseDetail == _enterpriseDetail) return;

    await EnterpriseConfigApi.addAndEdit(_enterpriseDetail);
    _oldEnterpriseDetail = EnterpriseDetail.fromJson(_enterpriseDetail.toJson());
  }

  @override
  Widget build(BuildContext context) {
    return AppLoading(
      isLoading: _isLoading,
      padding: const EdgeInsets.all(10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 20,
        children: [
          _buildEnterpriseName(),
          _buildEnterpriseDescribe(),
          _buildEnterpriseAvatar(),
          Row(
            children: [
              Text('客户端登录配置', style: TextStyle(color: AppColors.textHint, fontSize: 12)),
              Expanded(child: Divider(height: 1, color: AppColors.textHint)),
            ],
          ),
          _buildEnterpriseSign(),
        ],
      ),
    );
  }

  /// 企业名称
  Widget _buildEnterpriseName() {
    /// 是否校验通过
    var valid = false;
    String? validator(String? value) {
      if (value == null || value.isEmpty) {
        return '请输入企业名称';
      }
      valid = true;
      return null;
    }

    return AppInput(
      width: 1000,
      required: true,
      controller: _nameController,
      maxLength: 30,
      labelWidth: labelWidth,
      labelPosition: LabelPosition.left,
      label: '企业名称',
      hintText: '企业名称',
      showClearButton: false,
      inputFormatters: [NoSpecialCharsFormatter()],
      validator: validator,
      onChanged: (value) {
        _enterpriseDetail.enterpriseName = value;
      },
      onBlur: () {
        // 验证当前输入框
        if (valid) _editEnterpriseInfo();
      },
    );
  }

  /// 企业描述
  Widget _buildEnterpriseDescribe() {
    return AppInput(
      width: 1000,
      labelWidth: labelWidth,
      controller: _describeController,
      maxLength: 200,
      maxLines: 5,
      labelPosition: LabelPosition.left,
      label: '企业描述',
      hintText: '企业描述',
      onChanged: (value) {
        _enterpriseDetail.enterpriseDescribe = value;
      },
      onBlur: _editEnterpriseInfo,
    );
  }

  /// 企业头像
  Widget _buildEnterpriseAvatar() {
    double width = 80;
    double height = 80;

    return Row(
      children: [
        SizedBox(width: labelWidth, child: Text('企业头像')),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            MouseRegion(
              cursor: SystemMouseCursors.click,
              onEnter: (_) => setState(() => _isAvatarHovering = true),
              onExit: (_) => setState(() => _isAvatarHovering = false),
              child: GestureDetector(
                onTap: _onChangeEnterpriseAvatar,
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    ImageCacheUtil.cachedNetworkImage(
                      imageUrl: _enterpriseDetail.enterpriseAvatarImage,
                      width: width,
                      height: height,
                    ),
                    if (_isAvatarHovering)
                      Container(
                        width: width,
                        height: height,
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.5),
                          borderRadius: BorderRadius.circular(AppRadiusSize.radius4),
                        ),
                        child: Center(
                          child: Text('修改图片', style: TextStyle(color: Colors.white, fontSize: 12)),
                        ),
                      ),
                  ],
                ),
              ),
            ),
            SizedBox(height: 10),
            Text(
              '建议上传图片尺寸480px*480px以上',
              style: TextStyle(color: AppColors.textHint, fontSize: 12),
            ),
          ],
        ),
      ],
    );
  }

  /// 企业标识
  Widget _buildEnterpriseSign() {
    double width = 80;
    double height = 80;

    return Row(
      children: [
        SizedBox(width: labelWidth, child: Text('客户端标识')),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            MouseRegion(
              cursor: SystemMouseCursors.click,
              onEnter: (_) => setState(() => _isSignHovering = true),
              onExit: (_) => setState(() => _isSignHovering = false),
              child: GestureDetector(
                onTap: _onChangeEnterpriseSign,
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    ImageCacheUtil.cachedNetworkImage(
                      imageUrl: _enterpriseDetail.enterpriseClientImage,
                      width: width,
                      height: height,
                    ),
                    if (_isSignHovering)
                      Container(
                        width: width,
                        height: height,
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.5),
                          borderRadius: BorderRadius.circular(AppRadiusSize.radius4),
                        ),
                        child: Center(
                          child: Text('修改图片', style: TextStyle(color: Colors.white, fontSize: 12)),
                        ),
                      ),
                  ],
                ),
              ),
            ),
            SizedBox(height: 10),
            AppButton(
              text: '登录页预览效果',
              type: ButtonType.primary,
              size: ButtonSize.small,
              textOnly: true,
              onPressed: _loginPagePreview,
            ),
            SizedBox(height: 10),

            Text(
              '建议上传图片尺寸480px*480px以上',
              style: TextStyle(color: AppColors.textHint, fontSize: 12),
            ),
          ],
        ),
      ],
    );
  }

  /// 修改企业头像
  void _onChangeEnterpriseAvatar() async {
    PlatformFile? file = await FileUtil.pickSingleFile(type: FileType.image);
    if (file != null && file.bytes != null) {
      if (!mounted) return;

      UploadResponse? res = await ImageCropDialog.show(context: context, imageFile: file);

      if (res == null) return;

      setState(() {
        _enterpriseDetail.enterpriseAvatarImage = res.path;
        _enterpriseDetail.enterpriseAvatarImageId = res.fileId;
      });
      await _editEnterpriseInfo();
    }
  }

  /// 修改企业标识
  void _onChangeEnterpriseSign() async {
    PlatformFile? file = await FileUtil.pickSingleFile(type: FileType.image);
    if (file != null && file.bytes != null) {
      if (!mounted) return;

      UploadResponse? res = await ImageCropDialog.show(context: context, imageFile: file);

      if (res == null) return;

      setState(() {
        _enterpriseDetail.enterpriseClientImage = res.path;
        _enterpriseDetail.enterpriseClientImageId = res.fileId;
      });
      await _editEnterpriseInfo();
    }
  }

  /// 登录页预览效果
  void _loginPagePreview() {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.white,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(AppRadiusSize.radius6),
            child: Stack(
              children: [
                // 使用AbsorbPointer禁用所有交互
                AbsorbPointer(
                  absorbing: true,
                  child: FittedBox(
                    fit: BoxFit.scaleDown,
                    child: SizedBox(width: 1200, height: 700, child: const LoginPage()),
                  ),
                ),
                // 关闭按钮
                Positioned(
                  top: 10,
                  right: 10,
                  child: AppButton(
                    type: ButtonType.transparent,
                    iconData: Icons.clear,
                    color: context.icon200,
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
