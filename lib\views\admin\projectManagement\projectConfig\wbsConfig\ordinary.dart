import 'package:flutter/material.dart';
import 'package:octasync_client/imports.dart';
import 'package:octasync_client/api/wbs_field_config.dart';
import 'package:octasync_client/models/pages_model/pages_model.dart';
import 'package:octasync_client/models/wbs_field_config/field_source_enum.dart';
import 'package:octasync_client/models/wbs_field_config/wbs_config_type_enum.dart';
import 'package:octasync_client/models/wbs_field_config/wbs_field_config.dart';

/// 每列数据模型
class HeadModel {
  final String title;
  final String? key; // key对应后端字段
  final int? flex; // 空间分配
  final String? tooltip;

  HeadModel({required this.title, this.key, this.flex = 1, this.tooltip});
}

/// 样式常量
class _OrdinaryConstants {
  static const double rowHeight = 40.0;
  static const Duration animationDuration = Duration(milliseconds: 300);
  static const EdgeInsets cellPadding = EdgeInsets.symmetric(horizontal: 10, vertical: 5);
  static const EdgeInsets headerPadding = EdgeInsets.all(10);
  static const double iconSpacing = 5.0;
  static const double sectionSpacing = 10.0;
  static const double dragHandleSpacing = 10.0;
}

/// 可展开折叠的数据列表组件
class _ExpandableDataSection extends StatefulWidget {
  final String title;
  final List<WbsFieldConfig> items;
  final List<HeadModel> columns;
  final Widget Function(WbsFieldConfig item, List<HeadModel> columns) itemBuilder;
  final bool enableReorder;
  final Function(int oldIndex, int newIndex)? onReorder;

  const _ExpandableDataSection({
    required this.title,
    required this.items,
    required this.columns,
    required this.itemBuilder,
    this.enableReorder = false,
    this.onReorder,
  });

  @override
  State<_ExpandableDataSection> createState() => _ExpandableDataSectionState();
}

class _ExpandableDataSectionState extends State<_ExpandableDataSection> {
  bool _isExpanded = true;

  void _toggleExpand() {
    setState(() {
      _isExpanded = !_isExpanded;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: _buildContainerDecoration(context),
      child: Column(children: [_buildHeader(context), _buildContent(context)]),
    );
  }

  /// 容器装饰
  BoxDecoration _buildContainerDecoration(BuildContext context) {
    return BoxDecoration(
      color: context.background200,
      border: Border.all(color: context.border300),
      borderRadius: BorderRadius.circular(AppRadiusSize.radius4),
    );
  }

  /// 头部
  Widget _buildHeader(BuildContext context) {
    return Material(
      color: context.background200,
      child: InkWell(
        onTap: _toggleExpand,
        child: Container(
          height: _OrdinaryConstants.rowHeight,
          padding: _OrdinaryConstants.headerPadding,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              AnimatedRotation(
                turns: _isExpanded ? 0.5 : 0,
                duration: _OrdinaryConstants.animationDuration,
                child: Icon(Icons.keyboard_arrow_down, size: 16, color: context.icon100),
              ),
              SizedBox(width: _OrdinaryConstants.iconSpacing),
              Text(widget.title, style: TextStyle(color: AppColors.textSecondary)),
              const SizedBox(width: 20),
              Text('${widget.items.length}条记录', style: Theme.of(context).textTheme.labelMedium),
            ],
          ),
        ),
      ),
    );
  }

  /// 内容
  Widget _buildContent(BuildContext context) {
    return AnimatedCrossFade(
      duration: _OrdinaryConstants.animationDuration,
      crossFadeState: _isExpanded ? CrossFadeState.showSecond : CrossFadeState.showFirst,
      firstChild: Container(),
      secondChild: widget.enableReorder ? _buildReorderableList() : _buildNormalList(),
      firstCurve: Curves.easeOutQuad,
      secondCurve: Curves.easeInQuad,
      sizeCurve: Curves.easeInOutQuad,
    );
  }

  /// 普通列表
  Widget _buildNormalList() {
    return Column(
      children: widget.items.map((item) => widget.itemBuilder(item, widget.columns)).toList(),
    );
  }

  /// 可重排序列表
  Widget _buildReorderableList() {
    return Container(
      height: _OrdinaryConstants.rowHeight * widget.items.length,
      color: context.backgroundWhite,
      child: ReorderableListView.builder(
        itemCount: widget.items.length,
        buildDefaultDragHandles: false,
        itemBuilder: (context, index) => widget.itemBuilder(widget.items[index], widget.columns),
        onReorder: widget.onReorder ?? (_, __) {},
      ),
    );
  }
}

/// 字段配置-普通组件
class Ordinary extends StatefulWidget {
  const Ordinary({super.key});

  @override
  State<Ordinary> createState() => _OrdinaryState();
}

class _OrdinaryState extends State<Ordinary> {
  bool _isLoading = false;

  /// 请求
  final listParams = {
    'Keywords': '',
    'PageIndex': 1,
    'PageSize': 999,
    'WBSConfigTypeenum': WBSConfigTypeEnum.normalComponent.value,
  };
  final Debounce _debounceSearch = Debounce(delay: Duration(milliseconds: 1000));
  final TextEditingController _searchController = TextEditingController();

  /// 页面数据
  List<WbsFieldConfig> list = [];
  List<WbsFieldConfig> _otherInfoList = [];
  List<WbsFieldConfig> _baseInfoList = [];
  List<HeadModel> columnList = [
    HeadModel(key: 'displayName', flex: 2, title: '基本信息'),
    HeadModel(key: 'enabled', flex: 1, title: '启用'),
    HeadModel(
      key: 'visible',
      flex: 1,
      title: '默认显示在列表/详情',
      tooltip: '开启后，该字段将自动显示在WBS列表视图以及WBS组件详情中，无需手动添加',
    ),
    HeadModel(
      key: 'requiredCreate',
      flex: 1,
      title: '创建时必填项',
      tooltip: '开启后，各WBS组件创建时候需要必填，否则无法创建成功',
    ),
    HeadModel(
      key: 'requiredSubmit',
      flex: 1,
      title: '提交时必填项',
      tooltip: '开启后，各WBS组件该字段如不进行填写，则无法提交审核',
    ),
    HeadModel(
      key: 'editable',
      flex: 1,
      title: '项目配置修改',
      tooltip: '勾选后，允许在项目配置中修改该字段的配置项；项目经理提交项目级配置审核并经同意后，可推翻企业级项目配置规则',
    ),
  ];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      getListPage();
    });
  }

  @override
  void dispose() {
    _debounceSearch.dispose();
    super.dispose();
  }

  /// 获取列表
  void getListPage() async {
    try {
      setState(() {
        _isLoading = true;
      });
      final res = await WBSFieldConfigApi.getListPage(listParams);
      final pages = PagesModel<WbsFieldConfig>.fromJson(
        res,
        (json) => WbsFieldConfig.fromJson(json as Map<String, dynamic>),
      );
      list = pages.items;
      list.sort((a, b) => a.orderIndex.compareTo(b.orderIndex));

      // 基础信息列表
      _baseInfoList =
          list.where((item) => item.fieldSourceenum == FieldSourceEnum.default_.value).toList();
      // 其他信息列表
      _otherInfoList =
          list.where((item) => item.fieldSourceenum == FieldSourceEnum.custom.value).toList();
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 编辑单项
  void editItem(WbsFieldConfig item) async {
    try {
      await WBSFieldConfigApi.edit(item.toJson());
    } catch (e) {
      ToastManager.error('编辑失败');
      getListPage();
    }
  }

  void search(String value) {
    listParams['Keywords'] = value;
    getListPage();
  }

  /// 切换复选框状态
  void changeCheck(bool? value, String key, WbsFieldConfig item) {
    final newValue = value ?? false;
    setState(() {
      _updateItemProperty(item, key, newValue);
    });
    editItem(item);
  }

  /// 更新项目属性
  void _updateItemProperty(WbsFieldConfig item, String key, bool value) {
    switch (key) {
      case 'enabled':
        item.enabled = value;
      case 'visible':
        item.visible = value;
      case 'requiredCreate':
        item.requiredCreate = value;
      case 'requiredSubmit':
        item.requiredSubmit = value;
      case 'editable':
        item.editable = value;
    }
  }

  /// 获取项目属性值
  bool _getItemProperty(WbsFieldConfig item, String key) {
    return switch (key) {
      'enabled' => item.enabled,
      'visible' => item.visible,
      'requiredCreate' => item.requiredCreate,
      'requiredSubmit' => item.requiredSubmit,
      'editable' => item.editable,
      _ => false,
    };
  }

  /// 处理拖拽重排序
  void _handleReorder(int oldIndex, int newIndex) {
    setState(() {
      // 调整新索引位置
      if (oldIndex < newIndex) newIndex--;

      // 在缓存列表中重新排序
      final item = _otherInfoList.removeAt(oldIndex);
      _otherInfoList.insert(newIndex, item);

      // 更新排序索引并重新排序
      _updateOrderIndices();
    });
  }

  /// 更新排序索引
  void _updateOrderIndices() async {
    try {
      // 合并基本信息和重新排序后的其他信息
      list = [..._baseInfoList, ..._otherInfoList];

      // 更新所有项的orderIndex
      for (int i = 0; i < list.length; i++) {
        list[i].orderIndex = i;
      }
      _editOrderRequest();
    } catch (e) {
      // 如果更新失败，重新获取数据
      WidgetsBinding.instance.addPostFrameCallback((_) {
        getListPage();
      });
    }
  }

  /// 编辑排序
  void _editOrderRequest() async {
    try {
      final orderList =
          list.map((e) {
            return {'Id': e.id, 'OrderIndex': e.orderIndex};
          }).toList();
      await WBSFieldConfigApi.setOrder(orderList);
    } catch (e) {
      ToastManager.error('排序失败');
      getListPage();
    }
  }

  /// 获取每列的flex值
  int getColFlex(int index) {
    try {
      return columnList[index].flex ?? 1;
    } catch (e) {
      return 1;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _topArea(),
        SizedBox(height: _OrdinaryConstants.sectionSpacing),
        _tableHeader(),
        SizedBox(height: _OrdinaryConstants.sectionSpacing),
        Expanded(
          child: SingleChildScrollView(
            scrollDirection: Axis.vertical,
            child: AppLoading(
              isLoading: _isLoading,
              child: Column(
                children: [
                  _buildBasicInfoSection(),
                  SizedBox(height: _OrdinaryConstants.sectionSpacing),
                  _buildOtherInfoSection(),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 基本信息区域
  Widget _buildBasicInfoSection() {
    return _ExpandableDataSection(
      title: '基本信息',
      items: _baseInfoList,
      columns: columnList,
      itemBuilder: (item, columns) => _buildTableRow(item),
    );
  }

  /// 其他信息区域
  Widget _buildOtherInfoSection() {
    return _ExpandableDataSection(
      title: '其他信息',
      items: _otherInfoList,
      columns: columnList,
      enableReorder: true,
      itemBuilder: (item, columns) {
        final index = _otherInfoList.indexOf(item);
        return _buildTableRow(item, showDragHandle: true, index: index);
      },
      onReorder: (oldIndex, newIndex) {
        _handleReorder(oldIndex, newIndex);
      },
    );
  }

  /// 单元格内容
  Widget _buildCellContent(WbsFieldConfig item, String key) {
    if (key == 'displayName') {
      return Text(item.displayName, textAlign: TextAlign.start, style: TextStyle(height: 2));
    }
    return Checkbox(
      value: _getItemProperty(item, key),
      onChanged: (value) => changeCheck(value, key, item),
    );
  }

  /// 表格行
  Widget _buildTableRow(WbsFieldConfig item, {bool showDragHandle = false, int? index}) {
    return Container(
      key: showDragHandle ? ValueKey(item) : null,
      height: _OrdinaryConstants.rowHeight,
      decoration: BoxDecoration(
        color: context.backgroundWhite,
        border: Border(bottom: BorderSide(color: context.border300)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children:
            columnList.asMap().entries.map((entry) {
              return Expanded(
                flex: getColFlex(entry.key),
                child: Container(
                  height: double.infinity,
                  padding: _OrdinaryConstants.cellPadding,
                  decoration: _buildCellBorder(context, entry.key),
                  child: _buildCellWithDragHandle(item, entry, showDragHandle, index),
                ),
              );
            }).toList(),
      ),
    );
  }

  /// 带拖拽手柄的单元格
  Widget _buildCellWithDragHandle(
    WbsFieldConfig item,
    MapEntry<int, HeadModel> entry,
    bool showDragHandle,
    int? index,
  ) {
    if (showDragHandle && entry.key == 0) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          ReorderableDragStartListener(
            index: index!,
            enabled: true,
            child: MouseRegion(
              cursor: SystemMouseCursors.click,
              child: const Icon(
                IconFont.xianxing_tuodongpaixu,
                color: AppColors.icon200,
                size: AppIconSize.small,
              ),
            ),
          ),
          SizedBox(width: _OrdinaryConstants.dragHandleSpacing),
          Expanded(child: SizedBox.expand(child: _buildCellContent(item, entry.value.key ?? ''))),
        ],
      );
    }
    return _buildCellContent(item, entry.value.key ?? '');
  }

  /// 单元格边框
  BoxDecoration _buildCellBorder(BuildContext context, int index) {
    return BoxDecoration(
      border: Border(
        right: BorderSide(
          color: index == columnList.length - 1 ? Colors.transparent : context.border300,
        ),
      ),
    );
  }

  /// 顶部区域
  Widget _topArea() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            AppInput(
              width: 300,
              prefixIcon: Icon(
                IconFont.xianxing_sousuo,
                size: AppIconSize.small,
                color: context.icon300,
              ),
              controller: _searchController,
              showClearButton: true,
              maxLength: 30,
              labelWidth: 0,
              labelPosition: LabelPosition.left,
              label: '',
              hintText: '请输入字段名称',
              onChanged: (value) {
                _debounceSearch.run(() => search(value));
              },
            ),
            //TODO:需要等添加字段组件开发完成
            AppButton(type: ButtonType.primary, text: ' 添加字段', onPressed: () {}),
          ],
        ),
      ],
    );
  }

  /// 表头
  Widget _tableHeader() {
    return Container(
      height: 40,
      decoration: BoxDecoration(
        color: context.background200,
        border: Border.all(color: context.border300),
        borderRadius: BorderRadius.circular(AppRadiusSize.radius4),
      ),
      child: Row(
        children: [
          ...columnList.asMap().entries.map(
            (entry) => Expanded(
              flex: getColFlex(entry.key),
              child: Container(
                decoration: BoxDecoration(
                  border: Border(
                    right: BorderSide(
                      color:
                          entry.key == columnList.length - 1
                              ? Colors.transparent
                              : context.border300,
                    ),
                  ),
                ),
                child: Center(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(entry.value.title),
                      if (entry.value.tooltip != null) ...[
                        SizedBox(width: 5),
                        AppCustomTooltip(
                          placement: TooltipPlacement.topCenter,
                          content: entry.value.tooltip!,
                          child: Icon(
                            IconFont.mianxing_shuoming,
                            size: AppIconSize.small,
                            color: context.icon300,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
