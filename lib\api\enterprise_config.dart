import 'package:octasync_client/commons/http_service.dart';

final _http = HttpService();

/// 企业信息配置
class EnterpriseConfigApi {
  static Future<dynamic> getDetail(data) {
    return _http.post('/Business/EnterpriseConfig/GetDetail', data: data);
  }

  static Future<dynamic> addAndEdit(data) {
    return _http.post('/Business/EnterpriseConfig/AddAndEdit', data: data);
  }

  static Future<dynamic> getLoginPageInfo(data) {
    return _http.post('/Business/EnterpriseConfig/GetLoginPage', data: data);
  }
}
