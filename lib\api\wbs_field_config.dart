import 'package:octasync_client/commons/http_service.dart';

final _http = HttpService();

const controller = '/Business/FieldConfig/';

/// 企业信息配置
class WBSFieldConfigApi {
  static Future<dynamic> add(data) {
    return _http.post('${controller}Add', data: data);
  }

  static Future<dynamic> delete(data) {
    return _http.post('${controller}Delete', data: data);
  }

  static Future<dynamic> edit(data) {
    return _http.post('${controller}Edit', data: data);
  }

  static Future<dynamic> getListPage(data) {
    return _http.post('${controller}GetListPage', data: data);
  }

  static Future<dynamic> getDetail(data) {
    return _http.post('${controller}GetDetail', data: data);
  }

  static Future<dynamic> setOrder(data) {
    return _http.post('${controller}setOrder', data: data);
  }
}
