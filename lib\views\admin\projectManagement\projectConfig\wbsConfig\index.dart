import 'package:flutter/material.dart';
import 'package:octasync_client/imports.dart';
import 'package:octasync_client/views/admin/projectManagement/projectConfig/wbsConfig/operation.dart';
import 'package:octasync_client/views/admin/projectManagement/projectConfig/wbsConfig/ordinary.dart';

class WebConfig extends StatefulWidget {
  const WebConfig({super.key});

  @override
  State<WebConfig> createState() => _WebConfigState();
}

class _WebConfigState extends State<WebConfig> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  int tabIndex = 0;
  List<Tab> tabs = [
    Tab(text: '普通组件', height: 35),
    Tab(text: '规划包', height: 35),
    Tab(text: '工作包', height: 35),
    Tab(text: '操作设置', height: 35),
  ];
  // 用于存储每个选项卡对应的内容
  Map<int, Widget> tabContents = {
    0: Center(child: Ordinary()),
    1: Center(child: Text('规划包内容')),
    2: Center(child: Text('工作包内容')),
    3: Center(child: Operation()),
  };

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: tabs.length, // 选项卡数量
      vsync: this as TickerProvider, // 动画同步
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 400,
          child: TabBar(
            padding: EdgeInsets.zero, // 移除整体内边距
            indicatorPadding: EdgeInsetsGeometry.only(bottom: 5),
            labelPadding: EdgeInsets.only(top: 0, bottom: 5), // 减小标签的垂直内边距
            controller: _tabController,
            indicatorColor: AppColors.primary, // 底部指示条颜色
            labelColor: context.textPrimary, // 选中标签颜色
            unselectedLabelColor: context.textPrimary, // 未选中标签颜色
            dividerHeight: 0,
            indicatorWeight: 2.0, // 指示条厚度
            indicatorSize: TabBarIndicatorSize.label, // 指示条与标签同宽
            onTap: (value) {
              setState(() {
                tabIndex = value;
              });
            },
            tabs: tabs,
          ),
        ),
        Divider(height: 1, color: context.border300),
        Expanded(
          child: Padding(
            padding: const EdgeInsets.all(10.0),
            child: tabContents[tabIndex] ?? SizedBox(),
          ),
        ),
      ],
    );
  }
}
