import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';

import 'package:crop_image/crop_image.dart';
import 'package:file_picker/file_picker.dart';
import 'package:octasync_client/models/upload/upload_response.dart';
import 'dart:async';
import 'package:octasync_client/commons/http_service.dart';
import 'package:jyt_components_package/jyt_components_package.dart';

/// 正方形图片裁剪弹窗组件
/// 用于裁剪图片为正方形，并提供实时预览功能
class ImageCropDialog extends StatefulWidget {
  /// 初始图片文件
  final PlatformFile? imageFile;

  /// 裁剪完成回调函数(返回服务器端图片路径)
  final void Function(UploadResponse response)? onCropped;

  /// 标题
  final dynamic title;

  const ImageCropDialog({super.key, this.imageFile, this.onCropped, this.title = '图片裁剪'});

  /// 显示图片裁剪对话框
  static Future<UploadResponse?> show({
    required BuildContext context,
    PlatformFile? imageFile,
    dynamic title = '图片裁剪',
    void Function(UploadResponse response)? onCropped,
  }) {
    return showDialog<UploadResponse?>(
      context: context,
      barrierDismissible: false,
      builder:
          (BuildContext context) =>
              ImageCropDialog(imageFile: imageFile, onCropped: onCropped, title: title),
    );
  }

  @override
  State<ImageCropDialog> createState() => _ImageCropDialogState();
}

class _ImageCropDialogState extends State<ImageCropDialog> {
  /// 图片数据
  Uint8List? _imageData;

  /// 预览图片数据
  Uint8List? _previewImageData;

  /// 裁剪控制器
  final controller = CropController(aspectRatio: 1, defaultCrop: const Rect.fromLTRB(0, 0, 1, 1));

  /// 用于强制重建裁剪器的key
  Key _cropperKey = UniqueKey();

  ///  提交按钮Loading
  bool _btnLoading = false;

  /// 防抖定时器
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();
    _imageData = widget.imageFile?.bytes;

    /// 延迟加载预览图
    Timer(const Duration(milliseconds: 500), _updatePreview);
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    super.dispose();
  }

  /// 选择图片
  Future<void> _pickImage() async {
    PlatformFile? file = await FileUtil.pickSingleFile(type: FileType.image);
    if (file != null && file.bytes != null) {
      setState(() {
        _previewImageData = null;
        _imageData = file.bytes!;
        _cropperKey = UniqueKey(); // 更新key以强制重建裁剪器
      });
      // 重置裁剪控制器
      controller.crop = const Rect.fromLTRB(0, 0, 1, 1);

      /// 延迟加载预览图
      Timer(const Duration(milliseconds: 500), _updatePreview);
    }
  }

  /// 提交
  Future<void> _confirm() async {
    if (_imageData == null) return;

    setState(() {
      _btnLoading = true;
    });

    try {
      // 使用裁剪控制器裁剪图片
      final ui.Image croppedImage = await controller.croppedBitmap();
      final ByteData? byteData = await croppedImage.toByteData(format: ui.ImageByteFormat.png);

      if (byteData != null) {
        final bytes = byteData.buffer.asUint8List();

        // 创建 PlatformFile
        final file = PlatformFile(
          name: 'cropped_image_${DateTime.now().millisecondsSinceEpoch}.png',
          size: bytes.length,
          bytes: bytes,
        );

        UploadResponse res = await HttpService().uploadFile(file);

        if (widget.onCropped != null) {
          widget.onCropped!(res);
        }

        // 关闭对话框并返回裁剪后的图片数据
        if (mounted) {
          Navigator.of(context).pop(res);
        }
      }
    } catch (e) {
      print('裁剪图片失败: $e');
    } finally {
      if (mounted) {
        setState(() {
          _btnLoading = false;
        });
      }
    }
  }

  /// 更新预览图片
  Future<void> _updatePreview() async {
    if (_imageData == null) return;

    try {
      final ui.Image croppedImage = await controller.croppedBitmap();
      final byteData = await croppedImage.toByteData(format: ui.ImageByteFormat.png);

      if (byteData != null && mounted) {
        setState(() {
          _previewImageData = byteData.buffer.asUint8List();
        });
      }
    } catch (e) {
      print('更新预览图片错误: $e');
    }
  }

  /// 防抖处理的预览更新
  void _debouncedUpdatePreview() {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 300), _updatePreview);
  }

  @override
  Widget build(BuildContext context) {
    return AppDialog(
      title: widget.title,
      width: 640,
      height: 456,
      showFooter: true,
      footer: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          AppButton(
            text: '重新选择',
            type: ButtonType.primary,
            size: ButtonSize.small,
            textOnly: true,
            onPressed: _pickImage,
          ),
          Row(
            children: [
              AppButton(
                text: '取消',
                type: ButtonType.default_,
                onPressed: () => Navigator.of(context).pop(),
              ),
              const SizedBox(width: 12),
              AppButton(
                text: '确定',
                type: ButtonType.primary,
                loading: _btnLoading,
                disabled: _imageData == null,
                onPressed: _imageData != null ? _confirm : null,
              ),
            ],
          ),
        ],
      ),
      child: _buildContent(),
    );
  }

  /// 构建内容区域
  Widget _buildContent() {
    if (_imageData == null) {
      return _buildEmptyState();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Container(
          width: 600,
          height: 192,
          decoration: BoxDecoration(
            border: Border.all(color: context.border300),
            borderRadius: BorderRadius.circular(AppRadiusSize.radius4),
          ),
          child: _buildCropper(),
        ),
        const SizedBox(height: 20),
        _buildPreviewArea(),
      ],
    );
  }

  /// 构建裁剪器
  Widget _buildCropper() {
    return CropImage(
      key: _cropperKey,
      controller: controller,
      image: Image.memory(_imageData!),
      gridColor: Theme.of(context).primaryColor,
      gridCornerSize: 25,
      gridInnerColor: Colors.transparent,
      gridThinWidth: 1,
      gridThickWidth: 3,
      scrimColor: Colors.black.withOpacity(0.5),
      alwaysShowThirdLines: false,
      onCrop: (details) => _debouncedUpdatePreview(),
    );
  }

  /// 构建预览区域
  Widget _buildPreviewArea() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      spacing: 10,
      children: [
        Text('效果预览'),
        Row(spacing: 10, children: [_buildPreviewImage(), _buildPreviewImage(isCircle: true)]),
      ],
    );
  }

  /// 构建预览图片
  Widget _buildPreviewImage({bool isCircle = false}) {
    double size = 64;
    double radius = isCircle ? size / 2 : AppRadiusSize.radius4;

    return ClipRRect(
      borderRadius: BorderRadius.circular(radius),
      child:
          _previewImageData != null
              ? Image.memory(_previewImageData!, width: size, height: size, fit: BoxFit.cover)
              : Container(
                width: size,
                height: size,
                decoration: BoxDecoration(
                  border: Border.all(color: context.border300),
                  borderRadius: BorderRadius.circular(radius),
                ),
                alignment: Alignment.center,
                child: Text(
                  '加载中...',
                  style: TextStyle(fontSize: 10, color: Theme.of(context).hintColor),
                ),
              ),
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.image, size: 64, color: Theme.of(context).iconTheme.color?.withOpacity(0.5)),
          const SizedBox(height: 16),
          Text('请选择要裁剪的图片', style: TextStyle(fontSize: 16, color: Theme.of(context).hintColor)),
          const SizedBox(height: 32),
          AppButton(text: '选择图片', type: ButtonType.primary, onPressed: _pickImage),
        ],
      ),
    );
  }
}
