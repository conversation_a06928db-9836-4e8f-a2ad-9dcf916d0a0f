import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:octasync_client/commons/http_service.dart';
import 'package:octasync_client/services/tray_service.dart';
import 'package:octasync_client/services/window_service.dart';
import 'package:provider/provider.dart';
import 'package:window_manager/window_manager.dart';

import 'package:octasync_client/routes/app_router.dart';
import 'package:octasync_client/providers/providers.dart';
import 'package:octasync_client/providers/theme_provider.dart';
import 'imports.dart';
import 'package:jyt_components_package/jyt_components_package.dart';

final navigatorKey = GlobalKey<NavigatorState>();

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  if (AppUtil.isDesktop) {
    // 初始化窗口服务
    await WindowService().init();

    WindowOptions windowOptions = const WindowOptions(
      size: Size(1440, 960),
      minimumSize: Size(1440, 960),
      center: true,
      backgroundColor: Colors.transparent,
      skipTaskbar: false,
      titleBarStyle: TitleBarStyle.hidden,
      windowButtonVisibility: false,
    );

    windowManager.waitUntilReadyToShow(windowOptions, () async {
      await windowManager.setAsFrameless();
      await windowManager.setHasShadow(true);
      await windowManager.show();
      await windowManager.focus();

      // 初始化系统托盘
      await TrayService().init();
    });
  }

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return Providers(
      child: Builder(
        builder: (context) {
          final appRouter = AppRouter();
          final router = appRouter.createRouter(context, navigatorKey: navigatorKey);
          StorageUtil.init(); // 初始化storage
          ToastManager.init(navigatorKey); // 初始化Toast
          LoadingManager.init(navigatorKey); // 初始化Loading
          HttpService.init(navigatorKey); // 请求方法

          return Consumer<ThemeProvider>(
            builder: (context, themeProvider, child) {
              return MaterialApp.router(
                title: 'Octasync',
                //国际化配置
                localizationsDelegates: [
                  GlobalMaterialLocalizations.delegate,
                  GlobalWidgetsLocalizations.delegate,
                  GlobalCupertinoLocalizations.delegate,
                ],
                supportedLocales: [const Locale('zh', 'CN'), const Locale('en', 'US')],
                theme: themeProvider.themeData,
                routerConfig: router,
                debugShowCheckedModeBanner: false,
                builder: (context, child) {
                  if (AppUtil.isDesktop) {
                    return WindowResizeHandler(child: Scaffold(body: child));
                  }
                  return Scaffold(body: child);
                },
              );
            },
          );
        },
      ),
    );
  }
}
