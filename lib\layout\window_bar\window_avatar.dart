import 'dart:async';

import 'package:flutter/material.dart';
import 'package:octasync_client/api/Employee.dart';
import 'package:octasync_client/providers/user_provider.dart';
import 'package:provider/provider.dart';
import 'package:octasync_client/main.dart'; // 导入main.dart以获取navigatorKey
import 'package:jyt_components_package/jyt_components_package.dart';

class WindowAvatar extends StatefulWidget {
  const WindowAvatar({super.key});

  @override
  State<WindowAvatar> createState() => _WindowAvatarState();
}

class _WindowAvatarState extends State<WindowAvatar> {
  OverlayEntry? _overlayEntry;
  bool _isMenuOpen = false;

  /// 获取OverlayState
  OverlayState? get _overlay => navigatorKey.currentState?.overlay;

  @override
  void dispose() {
    _closeMenu();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final userInfo = context.select((UserProvider model) => model.userInfo);

    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: () {
          _isMenuOpen ? _closeMenu() : _showAvatarMenu(context, userInfo);
        },
        child: ImageCacheUtil.cachedAvatarImage(imageUrl: userInfo?.avatar, size: 28),
      ),
    );
  }

  void _showAvatarMenu(BuildContext context, UserInfo? userInfo) {
    final overlay = _overlay;
    if (overlay == null) {
      debugPrint('WindowAvatar错误: 无法获取Overlay');
      return;
    }

    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final Offset position = renderBox.localToGlobal(Offset.zero);

    // 创建新的 OverlayEntry
    _overlayEntry = OverlayEntry(
      builder: (context) {
        return TapRegion(
          onTapUpOutside: (event) {
            // 必须加延迟否则会跟打开下拉时机冲突
            Timer(Duration(milliseconds: 0), () => _closeMenu());
          },
          child: Stack(
            children: [
              // 菜单内容
              Positioned(
                top: position.dy,
                left: position.dx + 30,
                child: Material(
                  color: context.backgroundWhite,
                  elevation: 8,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppRadiusSize.radius6),
                    side: BorderSide(color: context.border300, width: 1),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(AppRadiusSize.radius6),
                    child: SizedBox(
                      width: 240,
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // 用户信息部分
                          _buildUserInfoSection(context, userInfo),
                          _buildMenuItem(
                            context: context,
                            iconData: IconFont.xianxing_gexingzhuti,
                            title: '个性主题',
                            onTap: () {},
                          ),
                          _buildMenuItem(
                            context: context,
                            iconData: IconFont.xianxing_zhanghaoshezhi,
                            title: '账号设置',
                            onTap: () {},
                          ),
                          _buildMenuItem(
                            context: context,
                            iconData: IconFont.xianxing_xitongshezhi,
                            title: '系统设置',
                            onTap: () {},
                          ),
                          const Divider(height: 1),
                          _buildMenuItem(
                            context: context,
                            iconData: IconFont.xianxing_qiyeguanlihoutai,
                            title: '企业管理后台',
                            onTap: () {},
                          ),
                          const Divider(height: 1),
                          _buildMenuItem(
                            context: context,
                            iconData: IconFont.xianxing_tixing,
                            title: '关于',
                            onTap: () {},
                          ),
                          _buildMenuItem(
                            context: context,
                            iconData: IconFont.xianxing_tuichudenglu,
                            title: '退出登录',
                            onTap: () {
                              EmployeeApi.logOut(context);
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );

    // 添加到Overlay
    overlay.insert(_overlayEntry!);
    _isMenuOpen = true;
  }

  void _closeMenu() {
    if (_overlayEntry != null) {
      _overlayEntry!.remove();
      _overlayEntry = null;
      _isMenuOpen = false;
    }
  }

  Widget _buildUserInfoSection(BuildContext context, UserInfo? userInfo) {
    return Container(
      color: context.background300,
      padding: const EdgeInsets.all(15.0),
      child: IntrinsicHeight(
        child: Row(
          children: [
            ImageCacheUtil.cachedAvatarImage(
              imageUrl: userInfo?.avatar,
              radius: AppRadiusSize.radius6,
              size: 48,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Flexible(
                        child: Text(
                          userInfo?.name ?? '',
                          style: Theme.of(context).textTheme.headlineMedium,
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                        ),
                      ),
                      Icon(
                        IconFont.xianxing_danjiantou_you,
                        size: AppIconSize.small,
                        color: context.icon200,
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  //TODO:需要把硬编码替换后端数据
                  Text(
                    '深圳市佳运通电子有限公司',
                    style: Theme.of(context).textTheme.labelMedium,
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMenuItem({
    required BuildContext context,
    IconData? iconData,
    required String title,
    required VoidCallback onTap,
  }) {
    return Material(
      color: context.backgroundWhite,
      child: InkWell(
        onTap: () {
          onTap();
          _closeMenu();
        },
        child: Container(
          height: 40,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              if (iconData != null) Icon(iconData, size: AppIconSize.small, color: context.icon300),
              const SizedBox(width: 12),
              Expanded(child: Text(title, overflow: TextOverflow.ellipsis, maxLines: 1)),
            ],
          ),
        ),
      ),
    );
  }
}
