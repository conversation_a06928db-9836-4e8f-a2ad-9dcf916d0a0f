import 'package:flutter/material.dart';
import 'package:octasync_client/imports.dart';

/// 菜单项数据模型
class MenuItem {
  final String title;
  final List<MenuItem> subItems;
  final Widget? contentWidget;
  bool isExpanded;

  MenuItem({
    required this.title,
    this.subItems = const [],
    this.isExpanded = false,
    this.contentWidget,
  });

  /// 检查是否为叶子节点
  bool get isLeaf => subItems.isEmpty;

  /// 检查是否有内容组件
  bool get hasContent => contentWidget != null;
}

/// 项目菜单样式配置类
class ProjectMenuStyle {
  // 常量定义
  static const double defaultIndentSize = 16.0;
  static const double defaultFontSize = 13.0;
  static const Duration defaultAnimationDuration = Duration(milliseconds: 300);

  /// 菜单容器装饰
  final BoxDecoration? containerDecoration;

  /// 菜单列表内边距
  final EdgeInsetsGeometry listPadding;

  /// 菜单项内边距
  final EdgeInsetsGeometry itemPadding;

  /// 子菜单项基础内边距（不包含额外的水平缩进）
  final EdgeInsetsGeometry subItemPadding;

  /// 菜单项标题默认文本样式
  final TextStyle defaultTextStyle;

  /// 菜单项标题选中时文本样式
  final TextStyle selectedTextStyle;

  /// 父菜单项标题文本样式
  final TextStyle parentMenuTextStyle;

  /// 子菜单项标题文本样式
  final TextStyle childMenuTextStyle;

  /// 菜单项选中时背景色
  final Color? selectedBackgroundColor;

  /// 分隔线高度
  final double dividerHeight;

  /// 缩进大小
  final double indentSize;

  /// 动画持续时间
  final Duration animationDuration;

  // 缓存计算结果
  final Map<int, EdgeInsetsGeometry> _paddingCache = <int, EdgeInsetsGeometry>{};

  /// 创建项目菜单样式
  ProjectMenuStyle({
    this.containerDecoration,
    this.listPadding = const EdgeInsets.only(bottom: 8.0),
    this.itemPadding = const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
    this.subItemPadding = const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
    this.defaultTextStyle = const TextStyle(),
    this.selectedTextStyle = const TextStyle(fontWeight: FontWeight.normal),
    this.parentMenuTextStyle = const TextStyle(),
    this.childMenuTextStyle = const TextStyle(),
    this.selectedBackgroundColor,
    this.dividerHeight = 1.0,
    this.indentSize = defaultIndentSize,
    this.animationDuration = defaultAnimationDuration,
  });

  /// 默认样式
  factory ProjectMenuStyle.defaultStyle(BuildContext context) {
    final baseTextStyle = TextStyle(
      fontSize: defaultFontSize,
      color: context.textPrimary,
      fontWeight: FontWeight.normal,
    );

    return ProjectMenuStyle(
      containerDecoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        border: Border(right: BorderSide(color: context.border200, width: 1.0)),
      ),
      defaultTextStyle: baseTextStyle,
      parentMenuTextStyle: baseTextStyle.copyWith(fontWeight: FontWeight.w500),
      childMenuTextStyle: baseTextStyle,
      selectedBackgroundColor: context.activeGrayColor,
      selectedTextStyle: TextStyle(
        fontSize: defaultFontSize,
        color: AppColors.primary,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  /// 获取父菜单或子菜单样式
  TextStyle getMenuTextStyle(bool isParent) {
    return isParent ? parentMenuTextStyle : childMenuTextStyle;
  }

  /// 获取子菜单项内边距，包含额外的水平缩进
  /// 添加缓存以提高性能，限制最大缩进深度
  EdgeInsetsGeometry getChildPadding(int indentLevel) {
    // 限制最大缩进深度，防止过度缩进
    final clampedLevel = indentLevel.clamp(0, 10);

    // 检查缓存
    if (_paddingCache.containsKey(clampedLevel)) {
      return _paddingCache[clampedLevel]!;
    }

    // 计算并缓存结果
    final resolved = subItemPadding.resolve(TextDirection.ltr);
    final padding = EdgeInsets.only(
      left: resolved.left + (clampedLevel * indentSize),
      top: resolved.top,
      right: resolved.right,
      bottom: resolved.bottom,
    );

    _paddingCache[clampedLevel] = padding;
    return padding;
  }
}

/// 项目配置菜单组件
class ProjectMenu extends StatefulWidget {
  /// 菜单项选中回调
  final void Function(MenuItem menuItem) onMenuSelected;

  /// 当前选中的菜单项
  final String? selectedMenuItem;

  /// 菜单项数据
  final List<MenuItem> menuItems;

  /// 菜单样式
  final ProjectMenuStyle? style;

  /// 是否展开所有菜单项（不自动选择）
  final bool expandAll;

  /// 是否只展开第一个父菜单到最后一级并选择第一项
  final bool expandFirstParent;

  const ProjectMenu({
    super.key,
    required this.onMenuSelected,
    required this.menuItems,
    this.selectedMenuItem,
    this.style,
    this.expandAll = false,
    this.expandFirstParent = true,
  });

  @override
  State<ProjectMenu> createState() => _ProjectMenuState();
}

class _ProjectMenuState extends State<ProjectMenu> {
  // 缓存样式对象，避免重复创建
  ProjectMenuStyle? _cachedMenuStyle;

  /// 获取当前样式，如果未提供则使用默认样式
  ProjectMenuStyle get _menuStyle {
    _cachedMenuStyle ??= widget.style ?? ProjectMenuStyle.defaultStyle(context);
    return _cachedMenuStyle!;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 当依赖变化时清除缓存，确保样式更新
    _cachedMenuStyle = null;
  }

  @override
  void initState() {
    super.initState();

    // 根据参数优先级处理菜单展开
    if (widget.expandFirstParent) {
      _expandFirstParentToLeaf();
    } else if (widget.expandAll) {
      _expandAllMenuItems(widget.menuItems);
      // 注意：expandAll 只展开菜单，不会自动选择任何菜单项
    }
  }

  /// 递归展开所有菜单项，添加深度限制防止栈溢出
  void _expandAllMenuItems(List<MenuItem> items, [int depth = 0]) {
    // 限制最大递归深度
    if (depth > 20) {
      debugPrint('Warning: Menu depth exceeded maximum limit (20)');
      return;
    }

    for (var item in items) {
      // 只有含有子项的菜单才需要设置展开状态
      if (item.subItems.isNotEmpty) {
        item.isExpanded = true;
        // 递归设置子菜单的展开状态
        _expandAllMenuItems(item.subItems, depth + 1);
      }
    }
  }

  /// 展开第一个父菜单到最后一级并选择第一个叶子节点
  void _expandFirstParentToLeaf() {
    if (widget.menuItems.isEmpty) return;

    // 获取第一个父菜单
    MenuItem firstParent = widget.menuItems.first;

    // 设置第一个父菜单为展开状态
    firstParent.isExpanded = true;

    // 递归展开通向第一个叶子节点的路径，并返回该叶子节点
    MenuItem? leafNode = _expandPathToFirstLeaf(firstParent);

    if (leafNode != null) {
      // 通过回调通知父组件选择了该项
      WidgetsBinding.instance.addPostFrameCallback((_) {
        widget.onMenuSelected(leafNode);
      });
    }
  }

  /// 递归展开通向第一个叶子节点的路径，添加深度限制
  MenuItem? _expandPathToFirstLeaf(MenuItem parentItem, [int depth = 0]) {
    // 限制最大递归深度
    if (depth > 20) {
      debugPrint('Warning: Menu expansion depth exceeded maximum limit (20)');
      return null;
    }

    // 如果当前项没有子项，则它就是叶子节点
    if (parentItem.isLeaf) {
      return parentItem;
    }

    // 先检查第一个子项中是否有带contentWidget的叶子节点
    for (var item in parentItem.subItems) {
      if (item.isLeaf && item.hasContent) {
        // 找到有contentWidget的叶子节点，直接返回
        return item;
      }
    }

    // 如果没有带contentWidget的叶子节点，则获取第一个子项
    if (parentItem.subItems.isEmpty) return null;

    MenuItem firstChild = parentItem.subItems.first;

    // 展开这个子项
    firstChild.isExpanded = true;

    // 继续递归查找
    return _expandPathToFirstLeaf(firstChild, depth + 1);
  }

  @override
  Widget build(BuildContext context) {
    return Container(decoration: _menuStyle.containerDecoration, child: _buildMenuList());
  }

  // 构建菜单列表
  Widget _buildMenuList() {
    return ListView.builder(
      padding: _menuStyle.listPadding,
      itemCount: widget.menuItems.length,
      itemBuilder: (context, index) {
        return Padding(
          padding: EdgeInsets.symmetric(horizontal: 5),
          child: _buildMenuItem(widget.menuItems[index], true, 0), // 传入true表示顶级菜单，0为当前缩进级别
        );
      },
    );
  }

  // 构建菜单项
  Widget _buildMenuItem(MenuItem item, bool isParent, int indentLevel) {
    // 如果没有子菜单，则构建单个菜单项
    if (item.isLeaf) {
      return _buildSingleMenuItem(item, isParent, indentLevel);
    }

    // 有子菜单，构建可展开的菜单项
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 父菜单项
        _buildExpandableMenuItem(item, isParent, indentLevel),
        // 子菜单项
        _buildSubMenuItems(item, indentLevel),
      ],
    );
  }

  /// 构建可展开的菜单项头部
  Widget _buildExpandableMenuItem(MenuItem item, bool isParent, int indentLevel) {
    return InkWell(
      onTap: () {
        setState(() {
          item.isExpanded = !item.isExpanded;
        });
      },
      child: Container(
        padding: isParent ? _menuStyle.itemPadding : _menuStyle.getChildPadding(indentLevel),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Text(
                item.title,
                style: _menuStyle.getMenuTextStyle(isParent),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            // 展开/折叠图标
            AnimatedRotation(
              turns: item.isExpanded ? 0.5 : 0,
              duration: _menuStyle.animationDuration,
              child: Icon(Icons.keyboard_arrow_down, size: 16, color: context.icon100),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建子菜单项列表
  Widget _buildSubMenuItems(MenuItem item, int indentLevel) {
    return AnimatedCrossFade(
      duration: _menuStyle.animationDuration,
      crossFadeState: item.isExpanded ? CrossFadeState.showSecond : CrossFadeState.showFirst,
      firstChild: Container(), // 折叠状态，使用更轻量的组件
      secondChild: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children:
            item.subItems.map((subItem) {
              // 递归构建子菜单项，标记为子菜单，缩进级别+1
              return _buildMenuItem(subItem, false, indentLevel + 1);
            }).toList(),
      ), // 展开状态
      firstCurve: Curves.easeOutQuad,
      secondCurve: Curves.easeInQuad,
      sizeCurve: Curves.easeInOutQuad,
    );
  }

  // 构建单个菜单项
  Widget _buildSingleMenuItem(MenuItem item, bool isParent, int indentLevel) {
    final bool isSelected = widget.selectedMenuItem == item.title;

    return InkWell(
      onTap: () => widget.onMenuSelected(item),
      // 添加可访问性支持
      focusColor: _menuStyle.selectedBackgroundColor?.withValues(alpha: 0.1),
      hoverColor: _menuStyle.selectedBackgroundColor?.withValues(alpha: 0.05),
      child: Container(
        width: double.infinity,
        padding: isParent ? _menuStyle.itemPadding : _menuStyle.getChildPadding(indentLevel),
        decoration: BoxDecoration(
          color: isSelected ? _menuStyle.selectedBackgroundColor : null,
          borderRadius: BorderRadius.circular(AppRadiusSize.radius4),
        ),
        child: Text(
          item.title,
          style: isSelected ? _menuStyle.selectedTextStyle : _menuStyle.getMenuTextStyle(isParent),
          overflow: TextOverflow.ellipsis,
          maxLines: 1,
        ),
      ),
    );
  }
}
